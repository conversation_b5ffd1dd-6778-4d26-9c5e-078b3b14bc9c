<template>
    <div class="input-tag-example">
        <h3>InputToTag 组件使用示例</h3>
        
        <!-- 基础用法 -->
        <div class="example-section">
            <h4>基础用法</h4>
            <InputToTag
                v-model="basicTags"
                placeholder="输入标签后按回车添加"
                @change="handleBasicChange"
                @add="handleAdd"
                @remove="handleRemove"
            />
            <p>当前标签: {{ JSON.stringify(basicTags) }}</p>
        </div>
        
        <!-- 自定义字段映射 -->
        <div class="example-section">
            <h4>自定义字段映射</h4>
            <InputToTag
                v-model="customFieldTags"
                :props="{ name: 'label', id: 'value' }"
                placeholder="使用自定义字段映射"
                @change="handleCustomFieldChange"
            />
            <p>当前标签: {{ JSON.stringify(customFieldTags) }}</p>
        </div>
        
        <!-- 限制和验证 -->
        <div class="example-section">
            <h4>限制和验证</h4>
            <InputToTag
                v-model="limitedTags"
                :max-tags="3"
                :min-length="2"
                :max-length="10"
                :allow-duplicates="false"
                :validator="customValidator"
                placeholder="最多3个标签，长度2-10字符，不允许重复"
                @exceed-limit="handleExceedLimit"
                @duplicate="handleDuplicate"
            />
            <p>当前标签: {{ JSON.stringify(limitedTags) }}</p>
        </div>
        
        <!-- 只读模式 -->
        <div class="example-section">
            <h4>只读模式</h4>
            <InputToTag
                v-model="readonlyTags"
                :readonly="true"
                tag-type="success"
            />
        </div>
        
        <!-- 不同标签类型 -->
        <div class="example-section">
            <h4>不同标签类型</h4>
            <InputToTag
                v-model="typedTags"
                tag-type="warning"
                placeholder="警告类型标签"
            />
        </div>
    </div>
</template>

<script>
import InputToTag from './InputToTag.vue';

export default {
    name: 'InputToTagExample',
    components: {
        InputToTag
    },
    data() {
        return {
            // 基础标签
            basicTags: [
                { name: '前端', id: '1' },
                { name: 'Vue.js', id: '2' }
            ],
            
            // 自定义字段映射标签
            customFieldTags: [
                { label: '技术', value: 'tech' },
                { label: '开发', value: 'dev' }
            ],
            
            // 限制标签
            limitedTags: [
                { name: 'JavaScript', id: 'js' }
            ],
            
            // 只读标签
            readonlyTags: [
                { name: '已完成', id: 'done' },
                { name: '已审核', id: 'reviewed' }
            ],
            
            // 类型标签
            typedTags: [
                { name: '注意', id: 'warning1' }
            ]
        };
    },
    methods: {
        // 基础变化处理
        handleBasicChange(outputData, internalData) {
            console.log('基础标签变化:', outputData, internalData);
        },
        
        // 自定义字段变化处理
        handleCustomFieldChange(outputData, internalData) {
            console.log('自定义字段标签变化:', outputData, internalData);
        },
        
        // 添加标签处理
        handleAdd(newTag, allTags) {
            console.log('添加标签:', newTag, '所有标签:', allTags);
            this.$message.success(`成功添加标签: ${newTag.name}`);
        },
        
        // 删除标签处理
        handleRemove(removedTag, remainingTags) {
            console.log('删除标签:', removedTag, '剩余标签:', remainingTags);
            this.$message.info(`删除了标签: ${removedTag.name}`);
        },
        
        // 超出限制处理
        handleExceedLimit(maxTags) {
            console.log('超出最大标签数量限制:', maxTags);
        },
        
        // 重复标签处理
        handleDuplicate(tagName) {
            console.log('尝试添加重复标签:', tagName);
        },
        
        // 自定义验证函数
        customValidator(tagName) {
            if (tagName.includes('禁止')) {
                return '标签内容不能包含"禁止"字样';
            }
            if (/^\d+$/.test(tagName)) {
                return '标签不能是纯数字';
            }
            return null;
        }
    }
};
</script>

<style lang="less" scoped>
.input-tag-example {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
    
    .example-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        
        h4 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #303133;
        }
        
        p {
            margin-top: 10px;
            padding: 10px;
            background-color: #f5f7fa;
            border-radius: 4px;
            font-size: 12px;
            color: #606266;
            word-break: break-all;
        }
    }
}
</style>
