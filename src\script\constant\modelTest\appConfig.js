/**
 * 应用配置文件
 * 统一管理应用的各种配置项
 */

// 存储配置
export const STORAGE_CONFIG = {
    // 存储key前缀
    keyPrefix: 'mcpservice_',

    // localStorage配置
    localStorage: {
        maxSessions: 100,
        maxMessagesPerSession: 1000,
        autoCleanup: true,
        cleanupDays: 30
    },

    // IndexedDB配置
    indexedDB: {
        dbName: 'mcpservice_chatdb',
        dbVersion: 1,
        maxSessions: 500,
        maxMessagesPerSession: 5000,
        autoCleanup: true,
        cleanupDays: 90
    }
};

// API配置
export const API_CONFIG = {
    // 默认超时时间
    timeout: 300000,

    // 重试配置
    retry: {
        maxRetries: 3,
        retryDelay: 1000,
        retryMultiplier: 2
    },

    // 流式响应配置
    streaming: {
        bufferSize: 1024,
        updateInterval: 50, // ms
        timeout: 30000
    }
};

// UI配置
export const UI_CONFIG = {
    // 消息显示配置
    message: {
        maxHistoryLength: 50,
        enableMarkdown: true,
        enableCodeHighlight: true,
        autoScroll: true
    },

    // 会话配置
    session: {
        titleMaxLength: 50,
        autoGenerateTitle: true,
        titleFromFirstMessage: true
    },
};

// 错误处理配置
export const ERROR_CONFIG = {
    // 错误消息映射
    errorMessages: {
        network: '网络连接失败，请检查网络状态',
        timeout: '请求超时，请稍后重试',
        server: '服务器错误，请稍后重试',
        client: '请求参数错误',
        auth: '请求认证失败，请检查重试',
        unknown: '未知错误，请稍后重试'
    },
};

// 导出默认配置
export default {
    storage: STORAGE_CONFIG,
    api: API_CONFIG,
    ui: UI_CONFIG,
    error: ERROR_CONFIG,
};
