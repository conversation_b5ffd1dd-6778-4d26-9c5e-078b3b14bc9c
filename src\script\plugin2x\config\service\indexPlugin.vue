<template>
    <div class="config-service-container">
        <header class="config-service-container-header">MCP服务配置</header>
        <div class="config-service-container-search">
            <SearchBar :form-cols="searchFormCols" :form="searchForm">
                <el-button type="primary" @click="!isLoading && handleSearch()">查询</el-button>
                <el-button
                    style="margin-left: auto"
                    type="plain"
                    icon="el-icon-download"
                    @click="openImportDialog('excel')"
                    >excel导入
                </el-button>
                <el-button type="plain" icon="el-icon-download" @click="openImportDialog('json')"
                    >json导入
                </el-button>
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    @click="addServiceDialogVisible = true"
                    >新增服务</el-button
                >
            </SearchBar>
        </div>
        <main class="config-service-container-main">
            <CardTable
                v-loading="isLoading"
                :data="serviceTableData"
                :total="total"
                :layout="'total, prev, pager, next, sizes, jumper'"
                :pagination="pagination"
                :updateTable="getTableData"
                :cardConfig="cardConfig"
                @cardEvent="handleCardEvent"
            ></CardTable>
        </main>
        <!-- 导入弹窗 -->
        <ImportDialog
            :type="dialogType"
            :visible.sync="dialogVisible"
            @on-success="handleImportSuccess"
        />
        <!-- 新增服务弹窗 -->
        <el-dialog
            title="新增服务"
            :visible.sync="addServiceDialogVisible"
            width="736px"
            :close-on-click-modal="false"
            destroy-on-close
            @close="addServiceForm = $options.data().addServiceForm"
        >
            <SearchBar
                ref="addServiceFormRef"
                :form-cols="addServiceFormCols"
                :form="addServiceForm"
            />
            <div class="dialog-footer">
                <el-button @click="addServiceDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleAddService">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import SearchBar from '@/script/components/SearchBar.vue';
import CardTable from '@/script/components/tables/cardTable/index.vue';
import ImportDialog from '@/script/components/importDialog/index.vue';
import ConfigService from '@/script/api/module/config-service';

export default {
    name: 'ServiceConfig',
    components: {
        SearchBar,
        CardTable,
        ImportDialog
    },
    data() {
        return {
            isLoading: false,
            // 搜索表单
            searchFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'mcpDescription',
                        label: 'MCP服务描述：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-input',
                        prop: 'mcpName',
                        label: 'MCP服务名称：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'DropdownCheckBox',
                        prop: 'mcpCategory',
                        label: '',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '所属分类',
                            type: 'value',
                            options: [{ label: '默认分类', value: 1 }]
                        },
                        rules: [],
                        span: 2,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-select',
                        prop: 'isValid',
                        label: '状态：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: [
                            {
                                label: '全部',
                                value: 2
                            },
                            {
                                label: '起效',
                                value: 1
                            },
                            {
                                label: '不起效',
                                value: 0
                            }
                        ]
                    },
                    {
                        type: 'tool',
                        prop: 'tool',
                        span: 10,
                        isShow: true
                    }
                ]
            ],
            searchForm: {
                mcpDescription: '',
                mcpName: '',
                isValid: 2
            },
            // 导入弹窗
            dialogVisible: false,
            dialogType: 'excel', // 'excel' 或 'json'
            // 新增服务弹窗
            addServiceDialogVisible: false,
            addServiceFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'mcpName',
                        label: 'MCP名称：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 24,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-select',
                        prop: 'mcpCategory',
                        label: '所属分类：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: true,
                            filterable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 24,
                        isDisabled: false,
                        isShow: true,
                        opts: [
                            {
                                label: '默认分类',
                                value: 1
                            }
                        ]
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'mcpDescription',
                        label: '详细描述：',
                        labelWidth: '150px',
                        attrs: {
                            type: 'textarea',
                            rows: 3,
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 24,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'mcpVersion',
                        label: 'MCP服务版本号：',
                        labelWidth: '150px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 24,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-input',
                        prop: 'comment',
                        label: '其他备注：',
                        labelWidth: '150px',
                        attrs: {
                            type: 'textarea',
                            rows: 3,
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: false, message: '必填', trigger: 'blur' }],
                        span: 24,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ]
            ],
            addServiceForm: {
                mcpName: '',
                mcpDescription: '',
                mcpVersion: '',
                comment: ''
            },
            // 服务表格数据
            serviceTableData: [],
            total: 0,
            pagination: {
                curPage: 1,
                pageSize: 10
            },
            // 卡片配置
            cardConfig: {
                module: 'service',
                icon: {
                    0: require('../../../../img/common/service-inactive.png'),
                    1: require('../../../../img/common/service-active.png')
                },
                headerHideItem: ['setting', 'tenantKey', 'tag', 'copy'],
                navList: [
                    {
                        name: '基础信息',
                        value: 'ServiceBaseInfo'
                    },
                    {
                        name: '关联工具',
                        value: 'ServiceToolInfo'
                    },
                    {
                        name: '关联资源',
                        value: 'ServiceResourceInfo'
                    }
                ]
            }
        };
    },
    created() {
        this.getTableData(this.pagination);
    },
    methods: {
        handleSearch() {
            this.pagination.curPage = 1;
            this.getTableData(this.pagination);
        },
        getTableData({ curPage = 1, pageSize = this.pagination.pageSize }) {
            this.isLoading = true;
            ConfigService.getMCPInfoPage({
                pageNum: curPage,
                pageSize: pageSize,
                mcpDescription: this.searchForm.mcpDescription,
                mcpName: this.searchForm.mcpName,
                isValid: this.searchForm.isValid
            })
                .then((res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.serviceTableData = res.data.list
                            .map((item) => {
                                return {
                                    rawData: item,
                                    id: item.mcpId,
                                    name: item.mcpName,
                                    description: item.mcpDescription,
                                    isValid: item.isValid,
                                    createdTime: item.createdTime,
                                    lastUpdateTime: item.lastUpdateTime
                                };
                            })
                            .sort((a, b) => {
                                return new Date(b.lastUpdateTime) - new Date(a.lastUpdateTime);
                            });
                        this.total = res.data.total;
                    }
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        openImportDialog(val) {
            this.dialogType = val;
            this.dialogVisible = true;
        },
        // 导入成功回调
        handleImportSuccess(data) {
            console.log('导入成功:', data);
            // 处理导入结果数据
            this.$message.success('导入成功');
        },
        handleAddService() {
            this.$refs.addServiceFormRef.validForm().then((valid) => {
                if (valid) {
                    ConfigService.addMCPInfo(this.addServiceForm).then((res) => {
                        if (res.serviceFlag === 'TRUE') {
                            this.$message.success(res.returnMsg || '新增成功');
                            this.addServiceDialogVisible = false;
                            this.handleSearch();
                        } else {
                            this.$message.error(res.returnMsg || '新增失败');
                        }
                    });
                }
            });
        },
        handleCardEvent(event) {
            const eventMap = {
                // 展开卡片
                expand: () => {
                    ConfigService.getMCPDetails({ mcpId: event.params.id }).then((res) => {
                        if (res.serviceFlag === 'TRUE') {
                            const configInfo = res.data.configInfo;
                            const responseData = {
                                ...res.data,
                                configInfo:
                                    (typeof configInfo === 'object' &&
                                        JSON.stringify(configInfo, null, 2)) ||
                                    configInfo
                            };
                            event.callback(JSON.parse(JSON.stringify(responseData)));
                        } else {
                            this.$message.error(res.returnMsg || '获取服务详情失败');
                        }
                    });
                },

                // 保存卡片
                save: () => {
                    ConfigService.updateMCPInfo({
                        mcpId: event.params.mcpId,
                        mcpName: event.params.mcpName,
                        mcpDescription: event.params.mcpDescription,
                        mcpVersion: event.params.mcpVersion,
                        comment: event.params.comment
                    }).then((res) => {
                        event.callback(res.serviceFlag === 'TRUE');
                        this.handleServiceResponse(res, '保存成功', '保存失败');
                    });
                },

                // 删除卡片
                delete: () => {
                    ConfigService.deleteMCPInfo({
                        mcpIds: [event.params]
                    }).then((res) => {
                        this.handleServiceResponse(res, '删除成功', '删除失败');
                    });
                },

                // 切换卡片状态（有效/无效）
                valid: () => {
                    ConfigService.changeMCPValid({
                        mcpIds: [event.params.id],
                        isValid: +event.params.isValid
                    }).then((res) => {
                        this.handleServiceResponse(res, '切换成功', '切换失败');
                    });
                }
            };

            eventMap[event.type] && eventMap[event.type]();
        },
        handleServiceResponse(res, successMsg, errorMsg) {
            if (res.serviceFlag === 'TRUE') {
                this.$message.success(res.returnMsg || successMsg);
                this.handleSearch();
            } else {
                this.$message.error(res.returnMsg || errorMsg);
            }
        }
    }
};
</script>

<style scoped lang="less">
.config-service-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    &-header {
        height: 1.25rem;
        margin-left: 0.5rem;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.25rem;
    }
    &-search {
        height: 3.5rem;
        background: rgba(255, 255, 255, 0.96);
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0 1rem;
    }
    &-main {
        height: 0;
        flex: 1;
        display: flex;
        gap: 1rem;
    }
    .dialog-footer {
        text-align: right;
        margin-top: 32px;
    }
}
</style>
