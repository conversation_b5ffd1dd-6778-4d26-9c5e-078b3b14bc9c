<template>
    <div class="input-tag-container">
        <div
            class="tags-wrapper"
            :style="{ paddingTop: tags.length > 0 ? '8px' : '0' }"
            @click="handleWrapperClick"
        >
            <draggable
                v-model="tags"
                tag="div"
                item-key="id"
                :animation="150"
                ghost-class="ghost"
                class="tags-row custom-scrollbar"
            >
                <el-tag
                    v-for="tag in tags"
                    :key="tag.id"
                    :type="tagType"
                    size="mini"
                    :closable="!readonly"
                    :disable-transitions="false"
                    @close="handleClose(tag.id)"
                    class="tag-item"
                    @click.stop
                >
                    {{ tag.name }}
                </el-tag>
            </draggable>
            <el-input
                v-if="!readonly"
                ref="tagInput"
                v-model="inputValue"
                class="input-new-tag"
                :placeholder="placeholder"
                @keyup.enter.native="handleInputConfirm"
                @blur="handleInputConfirm"
                @click.stop
            ></el-input>
        </div>
    </div>
</template>

<script>
import draggable from 'vuedraggable';
export default {
    name: 'InputToTag',
    components: {
        draggable
    },
    props: {
        // 标签数组，支持v-model双向绑定[{ name: '标签名', id: '标签ID' }]
        value: {
            type: Array,
            default: () => [],
            validator(value) {
                return Array.isArray(value);
            }
        },
        // 字段映射配置
        props: {
            type: Object,
            default: () => ({
                name: 'name',
                id: 'id'
            })
        },
        // 输入框占位文本
        placeholder: {
            type: String,
            default: '请在完成一个标签的输入后使用回车键分隔'
        },
        // 标签类型
        tagType: {
            type: String,
            default: 'info',
            validator(value) {
                return ['success', 'info', 'warning', 'danger'].includes(value);
            }
        },
        // 最大允许的标签数量
        maxTags: {
            type: Number,
            default: Infinity,
            validator(value) {
                return value > 0;
            }
        },
        // 只读模式
        readonly: {
            type: Boolean,
            default: false
        },
        // 是否允许重复标签
        allowDuplicates: {
            type: Boolean,
            default: false
        },
        // 自定义验证函数
        validator: {
            type: Function,
            default: null
        },
        // 标签最小长度
        minLength: {
            type: Number,
            default: 1
        },
        // 标签最大长度
        maxLength: {
            type: Number,
            default: 50
        }
    },
    data() {
        return {
            inputValue: '',
            localTags: []
        };
    },
    computed: {
        tags: {
            get() {
                return this.localTags;
            },
            set(val) {
                // 重新分配ID确保连续性
                this.localTags = val.map((item, index) => ({
                    ...item,
                    id: index + 1
                }));
                this.emitChange(this.localTags);
            }
        }
    },
    created() {
        this.initLocalTags();
    },
    watch: {
        value: {
            handler() {
                this.initLocalTags();
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        // 初始化本地标签数据
        initLocalTags() {
            this.localTags = this.value.map((item, index) => ({
                ...item, // 保留原始数据的所有字段
                name: item[this.props.name] || item.name,
                id: index + 1
            }));
        },

        // 重新分配所有标签的ID（从1开始连续编号）
        reassignIds() {
            this.localTags = this.localTags.map((item, index) => ({
                ...item,
                id: index + 1
            }));
        },

        // 验证标签内容
        validateTag(tagName) {
            const errors = [];

            // 长度验证
            if (tagName.length < this.minLength) {
                errors.push(`标签长度不能少于${this.minLength}个字符`);
            }
            if (tagName.length > this.maxLength) {
                errors.push(`标签长度不能超过${this.maxLength}个字符`);
            }

            // 重复性验证
            if (!this.allowDuplicates && this.tags.find((item) => item.name === tagName)) {
                errors.push('标签已存在');
            }

            // 数量限制验证
            if (this.tags.length >= this.maxTags) {
                errors.push(`最多只能添加${this.maxTags}个标签`);
            }

            // 自定义验证
            if (this.validator && typeof this.validator === 'function') {
                const customError = this.validator(tagName);
                if (customError) {
                    errors.push(customError);
                }
            }

            return errors;
        },

        // 发送数据变化事件
        emitChange(tags) {
            const outputData = tags.map((item) => {
                // 保留原始数据的所有字段，然后更新映射字段
                const obj = { ...item };
                obj[this.props.name] = item.name;
                obj[this.props.id] = item.id;

                return obj;
            });

            this.$emit('input', outputData);
            this.$emit('change', outputData, tags);
        },

        // 删除标签
        handleClose(id) {
            if (this.readonly) return;

            const removedTag = this.tags.find((item) => item.id === id);
            const newTags = this.tags.filter((item) => item.id !== id);

            // 重新分配ID
            this.localTags = newTags.map((item, index) => ({
                ...item,
                id: index + 1
            }));

            this.$emit('remove', removedTag, this.localTags);
        },

        // 确认输入
        handleInputConfirm() {
            const inputValue = this.inputValue.trim();
            if (!inputValue) {
                this.inputValue = '';
                return;
            }

            const errors = this.validateTag(inputValue);
            if (errors.length > 0) {
                this.$message.warning(errors[0]);
                if (errors.some((error) => error.includes('已存在') || error.includes('最多'))) {
                    this.inputValue = '';
                }
                if (errors.some((error) => error.includes('已存在'))) {
                    this.$emit('duplicate', inputValue);
                }
                if (errors.some((error) => error.includes('最多'))) {
                    this.$emit('exceed-limit', this.maxTags);
                }
                return;
            }

            const newTag = {
                name: inputValue,
                id: this.tags.length + 1
            };
            const newTags = [...this.tags, newTag];

            // 重新分配ID确保连续性
            this.localTags = newTags.map((item, index) => ({
                ...item,
                id: index + 1
            }));

            this.inputValue = '';
            this.$emit('add', newTag, this.localTags);
        },

        // 点击容器聚焦输入框
        handleWrapperClick(e) {
            if (this.readonly) return;
            if (e.target === e.currentTarget) {
                this.$refs.tagInput.focus();
            }
        }
    }
};
</script>

<style lang="less" scoped>
.input-tag-container {
    width: 100%;

    .tags-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0 15px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        background-color: transparent;
        min-height: 84px;

        .tags-row {
            user-select: none;
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            width: 100%;
            max-height: 100px;
            overflow-y: auto;

            .tag-item {
                cursor: move;
            }
        }

        .input-new-tag {
            width: 100%;
            line-height: 32px;

            /deep/.el-input__inner {
                border: none;
                padding: 0;
                height: 32px;
                line-height: 32px;
                background-color: transparent;
                &:focus {
                    outline: none;
                    box-shadow: none;
                }
            }

            /deep/.el-input__wrapper {
                box-shadow: none !important;
                padding: 0 !important;
            }
        }
    }
}
.ghost {
    opacity: 0.6;
    background-color: #c5e9ff;
    border: 1px dashed #1890ff;
}
</style>
