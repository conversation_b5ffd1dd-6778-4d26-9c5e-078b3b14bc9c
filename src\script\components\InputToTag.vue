<template>
    <div class="input-tag-container">
        <div
            class="tags-wrapper"
            :style="{ paddingTop: tags.length > 0 ? '8px' : '0' }"
            @click="handleWrapperClick"
        >
            <draggable
                v-model="tags"
                tag="div"
                item-key="id"
                :animation="150"
                ghost-class="ghost"
                class="tags-row custom-scrollbar"
            >
                <el-tag
                    v-for="tag in tags"
                    :key="tag.id"
                    :type="tagType"
                    size="mini"
                    :closable="!readonly"
                    :disable-transitions="false"
                    @close="handleClose(tag.id)"
                    class="tag-item"
                    @click.stop
                >
                    {{ tag.name }}
                </el-tag>
            </draggable>
            <el-input
                v-if="!readonly"
                ref="tagInput"
                v-model="inputValue"
                class="input-new-tag"
                :placeholder="placeholder"
                @keyup.enter.native="handleInputConfirm"
                @blur="handleInputConfirm"
                @click.stop
            ></el-input>
        </div>
    </div>
</template>

<script>
import draggable from 'vuedraggable';
export default {
    name: 'InputToTag',
    components: {
        draggable
    },
    props: {
        // 标签数组，支持v-model双向绑定[{ name: '标签名', id: '标签ID' }]
        value: {
            type: Array,
            default: () => []
        },
        props: {
            type: Object,
            default: () => ({
                name: 'name',
                id: 'id'
            })
        },
        // 输入框占位文本
        placeholder: {
            type: String,
            default: '请在完成一个标签的输入后使用回车键分隔'
        },
        tagType: {
            type: String,
            default: 'info'
        },
        // 最大允许的标签数量
        maxTags: {
            type: Number,
            default: Infinity
        },
        readonly: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            inputValue: ''
        };
    },
    computed: {
        tags: {
            get() {
                console.log('tags get', this.value);

                return this.value.map((item) => {
                    item.name = item[this.props.name];
                    item.id = item[this.props.id];
                });
            },
            set(val) {
                console.log('tags set 1', val);
                const newVal = [];
                val.forEach((item) => {
                    const obj = new Object();
                    obj[this.props.name] = item.name;
                    obj[this.props.id] = item.id;
                    newVal.push(obj);
                });
                console.log('tags set 2', newVal);

                this.$emit('input', val);
                this.$emit('change', val);
            }
        }
    },
    created() {
        this.tags = this.value;
    },
    watch: {
        value: {
            handler(newVal) {
                // this.tags = newVal;
                console.log('watch value', newVal);
            },
            deep: true,
            immediate: true
        },
        tags: {
            handler(newVal) {
                console.log('watch tags', newVal);
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        handleClose(id) {
            if (this.readonly) return;
            this.$set(
                this,
                'tags',
                this.tags.filter((item) => item.id !== id)
            );
        },
        handleInputConfirm() {
            const inputValue = this.inputValue.trim();
            if (inputValue) {
                if (this.tags.length >= this.maxTags) {
                    this.$message.warning(`最多只能添加${this.maxTags}个标签`);
                    this.inputValue = '';
                    return;
                }
                if (this.tags.find((item) => item.name === inputValue)) {
                    this.$message.warning('标签已存在');
                    this.inputValue = '';
                    return;
                }
                this.$set(
                    this,
                    'tags',
                    this.tags.concat({ name: inputValue, id: this.tags.length + 1 })
                );
            }
            this.inputValue = '';
        },
        handleWrapperClick(e) {
            if (this.readonly) return;
            // 如果点击的是wrapper本身（而不是其中的标签或输入框）
            if (e.target === e.currentTarget) {
                this.$refs.tagInput.focus();
            }
        }
    }
};
</script>

<style lang="less" scoped>
.input-tag-container {
    width: 100%;

    .tags-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0 15px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        background-color: transparent;
        min-height: 84px;

        .tags-row {
            user-select: none;
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            width: 100%;
            max-height: 100px;
            overflow-y: auto;

            .tag-item {
                cursor: move;
            }
        }

        .input-new-tag {
            width: 100%;
            line-height: 32px;

            /deep/.el-input__inner {
                border: none;
                padding: 0;
                height: 32px;
                line-height: 32px;
                background-color: transparent;
                &:focus {
                    outline: none;
                    box-shadow: none;
                }
            }

            /deep/.el-input__wrapper {
                box-shadow: none !important;
                padding: 0 !important;
            }
        }
    }
}
.ghost {
    opacity: 0.6;
    background-color: #c5e9ff;
    border: 1px dashed #1890ff;
}
</style>
