import norMalConfig from '../normal-config';
let gateWay = '/selfanalyticsSevice';
if (process.env.DIST_ENV === 'pre-prod') {
    gateWay = '/mcpservice';
}

export default {
    // 新增选项内容
    add(params) {
        return norMalConfig('/optionContent/add', params, gateWay);
    },
    // 修改选项内容
    update(params) {
        return norMalConfig('/optionContent/update', params, gateWay);
    },
    // 查询详情
    getDetails(params) {
        return norMalConfig('/optionContent/getDetails', params, gateWay);
    },
    // 查询所有去重的选项内容值
    getDistinctOptionValues(params) {
        return norMalConfig('/optionContent/getDistinctOptionValues', params, gateWay);
    },
    // 改变生效状态
    changeValid(params) {
        return norMalConfig('/optionContent/changeValid', params, gateWay);
    },
    // 删除选项内容
    remove(params) {
        return norMalConfig('/optionContent/remove', params, gateWay);
    },
    // 分页查询
    page(params) {
        return norMalConfig('/optionContent/page', params, gateWay);
    }
};
