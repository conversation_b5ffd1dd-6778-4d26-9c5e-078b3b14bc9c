import norMalConfig from '../normal-config';
let gateWay = '/selfanalyticsSevice';
if (process.env.DIST_ENV === 'pre-prod') {
    gateWay = '/mcpservice';
}
/*
{"info":{"title":"MCP项目","description":"","version":"1.0.0","x-marks":[{"mark_id":"1","project_id":"0","name":"开发中","color":"#2857FF","is_sys_default":1,"is_default_mark":1},{"mark_id":"2","project_id":"0","name":"已完成","color":"#26CEA4","is_sys_default":1,"is_default_mark":-1},{"mark_id":"3","project_id":"0","name":"需修改","color":"#FFC01E","is_sys_default":1,"is_default_mark":-1},{"mark_id":"4","project_id":"0","name":"已废弃","color":"#FF2200","is_sys_default":1,"is_default_mark":-1}]},"openapi":"3.0.0","tags":[{"name":"选项内容管理","description":"","x-type":"folder","x-url":"","x-target-id":"2fb446a8f24002"}],"paths":{"/optionContent/add":{"post":{"x-target-id":"4efb51a22463001","x-protocol":"http","summary":"新增","description":"","tags":["选项内容管理"],"x-mark-id":"1","x-updated-at":"2025-08-28T10:43:19+08:00","x-updated-user-name":"吴祥居","requestBody":{"content":{"application/json":{"schema":{"type":"object"},"example":"{\n    \"sourceSystemId\": \"\",\n    \"sourceSystemName\": \"\",\n    \"operateUserId\": \"\",\n    \"token\": \"\",\n    \"gatewayTimeStamp\": \"\",\n    \"requestData\": {\n        \"columnName\": \"所属模块\",\n        \"modules\": [\n            1\n        ],\n        \"optionsType\": 1,\n        \"optionContents\": [\n            {\n                \"optionContent\": \"广州大屏\",\n                \"isDefaultOptions\": 1\n            }\n        ]\n    }\n}"}}},"responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"object"},"example":"{\n\t\"serviceFlag\": \"TRUE\",\n\t\"returnCode\": \"000001\",\n\t\"returnMsg\": \"操作成功\",\n\t\"data\": null\n}"}}},"404":{"description":"失败","content":{"application/json":{"schema":{"type":"object"},"example":""}}}}}},"/optionContent/update":{"post":{"x-target-id":"4efb51a22463002","x-protocol":"http","summary":"修改","description":"","tags":["选项内容管理"],"x-mark-id":"1","x-updated-at":"2025-08-28T09:05:58+08:00","x-updated-user-name":"吴祥居","requestBody":{"content":{"application/json":{"schema":{"type":"object"},"example":"{\n    \"sourceSystemId\": \"\",\n    \"sourceSystemName\": \"\",\n    \"operateUserId\": \"\",\n    \"token\": \"\",\n    \"gatewayTimeStamp\": \"\",\n    \"requestData\": {\n        \"columnId\": 1,\n        \"columnName\": \"\",\n        \"modules\": [\n        ],\n        \"optionsType\": 1,\n        \"optionContents\": [\n            {\n                \"optionId\": 1,\n                \"optionContent\": \"\",\n                \"columnId\": 1,\n                \"isValid\": 1,\n                \"isDefaultOptions\": 1\n            }\n        ]\n    },\n    \"userName\": \"\",\n    \"userId\": \"\",\n    \"userNameCN\": \"\",\n    \"authority\": \"\"\n}"}}},"responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"object"},"example":"{\n    \"serviceFlag\": \"\",\n    \"returnCode\": \"\",\n    \"returnMsg\": \"\",\n    \"data\": {\n    }\n}"}}},"404":{"description":"失败","content":{"application/json":{"schema":{"type":"object"},"example":""}}}}}},"/optionContent/getDetails":{"post":{"x-target-id":"4efb51a22463003","x-protocol":"http","summary":"查询详情","description":"","tags":["选项内容管理"],"x-mark-id":"1","x-updated-at":"2025-08-28T09:05:58+08:00","x-updated-user-name":"吴祥居","requestBody":{"content":{"application/json":{"schema":{"type":"object"},"example":"{\n    \"sourceSystemId\": \"\",\n    \"sourceSystemName\": \"\",\n    \"operateUserId\": \"\",\n    \"token\": \"\",\n    \"gatewayTimeStamp\": \"\",\n    \"requestData\": {\n        \"columnId\": 1,\n        \"columnName\": \"\",\n        \"modules\": [\n        ],\n        \"optionsType\": 1,\n        \"pageNum\": 1,\n        \"pageSize\": 10\n    },\n    \"userName\": \"\",\n    \"userId\": \"\",\n    \"userNameCN\": \"\",\n    \"authority\": \"\"\n}"}}},"responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"object"},"example":"{\n    \"columnId\": 1,\n    \"columnName\": \"\",\n    \"modulesList\": [\n    ],\n    \"optionsType\": 1,\n    \"optionsTypeName\": \"\",\n    \"optionContents\": [\n        {\n            \"optionId\": 1,\n            \"optionContent\": \"\",\n            \"columnId\": 1,\n            \"isValid\": 1,\n            \"isDefaultOptions\": 1\n        }\n    ]\n}"}}},"404":{"description":"失败","content":{"application/json":{"schema":{"type":"object"},"example":""}}}}}},"/optionContent/getDistinctOptionValues":{"post":{"x-target-id":"4efb51a22463004","x-protocol":"http","summary":"查询所有去重的选项内容值","description":"","tags":["选项内容管理"],"x-mark-id":"1","x-updated-at":"2025-08-28T09:05:58+08:00","x-updated-user-name":"吴祥居","responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"object"},"example":"{\n    \"serviceFlag\": \"\",\n    \"returnCode\": \"\",\n    \"returnMsg\": \"\",\n    \"data\": {\n    }\n}"}}},"404":{"description":"失败","content":{"application/json":{"schema":{"type":"object"},"example":""}}}}}},"/optionContent/changeValid":{"post":{"x-target-id":"4efb51a22463005","x-protocol":"http","summary":"改变生效状态","description":"","tags":["选项内容管理"],"x-mark-id":"1","x-updated-at":"2025-08-28T09:05:58+08:00","x-updated-user-name":"吴祥居","requestBody":{"content":{"application/json":{"schema":{"type":"object"},"example":"{\n    \"sourceSystemId\": \"\",\n    \"sourceSystemName\": \"\",\n    \"operateUserId\": \"\",\n    \"token\": \"\",\n    \"gatewayTimeStamp\": \"\",\n    \"requestData\": {\n        \"columnIds\": [\n        ],\n        \"isValid\": 1\n    },\n    \"userName\": \"\",\n    \"userId\": \"\",\n    \"userNameCN\": \"\",\n    \"authority\": \"\"\n}"}}},"responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"object"},"example":"{\n    \"serviceFlag\": \"\",\n    \"returnCode\": \"\",\n    \"returnMsg\": \"\",\n    \"data\": {\n    }\n}"}}},"404":{"description":"失败","content":{"application/json":{"schema":{"type":"object"},"example":""}}}}}},"/optionContent/remove":{"post":{"x-target-id":"4efb51a22463006","x-protocol":"http","summary":"删除","description":"","tags":["选项内容管理"],"x-mark-id":"1","x-updated-at":"2025-08-28T09:05:58+08:00","x-updated-user-name":"吴祥居","requestBody":{"content":{"application/json":{"schema":{"type":"object"},"example":"{\n    \"sourceSystemId\": \"\",\n    \"sourceSystemName\": \"\",\n    \"operateUserId\": \"\",\n    \"token\": \"\",\n    \"gatewayTimeStamp\": \"\",\n    \"requestData\": {\n        \"columnIds\": [\n        ],\n        \"isValid\": 1\n    },\n    \"userName\": \"\",\n    \"userId\": \"\",\n    \"userNameCN\": \"\",\n    \"authority\": \"\"\n}"}}},"responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"object"},"example":"{\n    \"serviceFlag\": \"\",\n    \"returnCode\": \"\",\n    \"returnMsg\": \"\",\n    \"data\": {\n    }\n}"}}},"404":{"description":"失败","content":{"application/json":{"schema":{"type":"object"},"example":""}}}}}},"/optionContent/page":{"post":{"x-target-id":"4efb622adc63000","x-protocol":"http","summary":"分页查询","description":"","tags":["选项内容管理"],"x-mark-id":"2","x-updated-at":"2025-08-28T10:27:13+08:00","x-updated-user-name":"吴祥居","requestBody":{"content":{"application/json":{"schema":{"type":"object"},"example":"{\n    \"sourceSystemId\": \"\",\n    \"sourceSystemName\": \"\",\n    \"operateUserId\": \"\",\n    \"token\": \"\",\n    \"gatewayTimeStamp\": \"\",\n    \"requestData\": {\n        \"columnId\": 1,\n        \"columnName\": \"\",\n        \"modules\": [\n            1\n        ],\n        \"optionsType\": 1,\n        \"pageNum\": 1,\n        \"pageSize\": 10\n    },\n    \"userName\": \"\",\n    \"userId\": \"\",\n    \"userNameCN\": \"\",\n    \"authority\": \"\"\n}"}}},"responses":{"200":{"description":"成功","content":{"application/json":{"schema":{"type":"object"},"example":"{\n\t\"serviceFlag\": \"TRUE\",\n\t\"returnCode\": \"000001\",\n\t\"returnMsg\": \"操作成功\",\n\t\"data\": {\n\t\t\"total\": 1,\n\t\t\"list\": [\n\t\t\t{\n\t\t\t\t\"columnId\": 1,\n\t\t\t\t\"columnName\": \"所属项目\",\n\t\t\t\t\"modulesList\": [\n\t\t\t\t\t1\n\t\t\t\t],\n\t\t\t\t\"optionsType\": 1,\n\t\t\t\t\"optionsTypeName\": null\n\t\t\t}\n\t\t],\n\t\t\"pageNum\": 1,\n\t\t\"pageSize\": 10,\n\t\t\"size\": 1,\n\t\t\"startRow\": 1,\n\t\t\"endRow\": 1,\n\t\t\"pages\": 1,\n\t\t\"prePage\": 0,\n\t\t\"nextPage\": 0,\n\t\t\"isFirstPage\": true,\n\t\t\"isLastPage\": true,\n\t\t\"hasPreviousPage\": false,\n\t\t\"hasNextPage\": false,\n\t\t\"navigatePages\": 8,\n\t\t\"navigatepageNums\": [\n\t\t\t1\n\t\t],\n\t\t\"navigateFirstPage\": 1,\n\t\t\"navigateLastPage\": 1\n\t}\n}"}}},"404":{"description":"失败","content":{"application/json":{"schema":{"type":"object"},"example":""}}}}}}},"servers":[]}
 */
export default {
    exampleApi(params) {
        return norMalConfig('/example/api', params, gateWay);
    },
};
