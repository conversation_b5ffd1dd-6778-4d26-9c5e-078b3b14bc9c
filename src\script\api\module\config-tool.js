import norMalConfig from '../normal-config';
let gateWay = '/selfanalyticsSevice';
if (process.env.DIST_ENV === 'pre-prod') {
    gateWay = '/mcpservice';
}

export default {
    // 工具列表查询接口
    getToolList(params) {
        return norMalConfig('/toolInfo/page', params, gateWay);
    },
    // 工具删除接口
    delTool(params) {
        return norMalConfig('/toolInfo/remove', params, gateWay);
    },
    // 工具修改接口
    updateTool(params) {
        return norMalConfig('/toolInfo/update', params, gateWay);
    },
    // 工具新增接口
    addTool(params) {
        return norMalConfig('/toolInfo/add', params, gateWay);
    },
    // 查看工具详情接口
    getToolDetail(params) {
        return norMalConfig('/toolInfo/getToolDetails', params, gateWay);
    },
    // 工具生效状态接口
    changeToolValid(params) {
        return norMalConfig('/toolInfo/changeValid', params, gateWay);
    },
    // 工具调用测试接口
    callToolTest(params) {
        return norMalConfig('/toolInfo/interfaceTest', params, gateWay);
    },
    // 获取工具分类接口
    getToolClassify(params) {
        return norMalConfig('/toolInfo/getToolClassify', params, gateWay);
    }
};
