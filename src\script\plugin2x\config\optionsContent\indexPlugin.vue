<template>
    <div class="options-content-container">
        <header class="options-content-container-header">选项内容配置</header>
        <div class="options-content-container-search">
            <SearchBar :form-cols="searchFormCols" :form="searchForm">
                <el-button type="primary" @click="!isLoading && handleSearch()">查询</el-button>
                <el-button
                    style="margin-left: auto"
                    type="primary"
                    icon="el-icon-plus"
                    @click="handleAddField"
                    >新增字段</el-button
                >
            </SearchBar>
        </div>
        <main class="options-content-container-main">
            <InlineCardTable
                v-loading="isLoading"
                :data="optionsTableData"
                :total="total"
                :layout="'total, prev, pager, next, sizes, jumper'"
                :pagination="pagination"
                :updateTable="getTableData"
                :cardConfig="cardConfig"
                @cardEvent="handleCardEvent"
            ></InlineCardTable>
        </main>
        <!-- 新增/编辑字段弹窗 -->
        <el-dialog
            :title="dialogMode === 'add' ? '新增字段' : '编辑字段'"
            :visible.sync="fieldDialogVisible"
            width="736px"
            :close-on-click-modal="false"
            destroy-on-close
            @open="handleDialogOpen"
            @close="handleDialogClose"
        >
            <SearchBar ref="addFieldFormRef" :form-cols="addFieldFormCols" :form="addFieldForm">
                <template #optionContents>
                    <InputToTag
                        v-model="addFieldForm.optionContents"
                        :props="{
                            id: 'optionId',
                            name: 'optionContent'
                        }"
                        tagType=""
                        placeholder="输入后按Enter即可创建一个选项"
                    />
                </template>
                <template #optionList>
                    <el-popover
                        placement="right"
                        width="280"
                        trigger="click"
                        v-model="optionPopoverVisible"
                        @show="loadAvailableOptions"
                    >
                        <div class="option-selector">
                            <div class="option-selector-content">
                                <el-tag
                                    v-for="option in availableOptions"
                                    size="small"
                                    :key="option"
                                    :type="isOptionSelected(option) ? 'info' : ''"
                                    class="option-tag"
                                    @click="handleSelectOption(option)"
                                >
                                    {{ option }}
                                </el-tag>
                                <div v-if="availableOptions.length === 0" class="no-options">
                                    暂无可选择的选项
                                </div>
                            </div>
                        </div>
                        <el-button slot="reference" type="plain" style="height: 32px"
                            >选择
                        </el-button>
                    </el-popover>
                </template>
            </SearchBar>
            <div class="dialog-footer">
                <el-button @click="fieldDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleSaveField">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import SearchBar from '@/script/components/SearchBar.vue';
import InputToTag from '@/script/components/InputToTag.vue';
import InlineCardTable from '@/script/components/tables/inlineCardTable/index.vue';
import { FITMODULE, OPTIONTYPE } from '@/script/constant/optionsContent/options';
import OptionsContent from '@/script/api/module/options-content';

export default {
    name: 'OptionsContentConfig',
    components: {
        SearchBar,
        InputToTag,
        InlineCardTable
    },
    data() {
        return {
            isLoading: false,
            // 搜索表单
            searchFormCols: [
                [
                    {
                        type: 'el-input',
                        prop: 'columnName',
                        label: '字段名称：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'el-select',
                        prop: 'modules',
                        label: '适用模块：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请选择',
                            clearable: true,
                            multiple: true,
                            'collapse-tags': true
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: FITMODULE
                    },
                    {
                        type: 'el-select',
                        prop: 'optionsType',
                        label: '选项形式：',
                        labelWidth: 'max-content',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: [{ label: '全部', value: '' }].concat(OPTIONTYPE)
                    },
                    {
                        type: 'tool',
                        prop: 'tool',
                        span: 12,
                        isShow: true
                    }
                ]
            ],
            searchForm: {
                columnName: '',
                modules: [],
                optionsType: ''
            },
            // 新增/编辑字段弹窗
            fieldDialogVisible: false,
            dialogMode: 'add', // 'add' | 'edit'
            editingFieldId: null,
            addFieldForm: {
                columnName: '',
                modules: '',
                optionsType: '',
                defaultOption: '',
                optionContents: []
            },
            // 选项内容表格数据
            optionsTableData: [],
            total: 0,
            pagination: {
                curPage: 1,
                pageSize: 10
            },
            // 卡片配置
            cardConfig: {
                icon: {
                    0: require('../../../../img/common/file-close.png'),
                    1: require('../../../../img/common/file-open.png')
                },
                headerHideItem: []
            },
            // 选项选择器相关
            optionPopoverVisible: false,
            availableOptions: []
        };
    },
    computed: {
        addFieldFormCols() {
            return [
                [
                    {
                        type: 'el-input',
                        prop: 'columnName',
                        label: '字段名称：',
                        labelWidth: '90px',
                        attrs: {
                            placeholder: '请输入',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'blur' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ],
                [
                    {
                        type: 'el-select',
                        prop: 'modules',
                        label: '适用模块：',
                        labelWidth: '90px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: true,
                            multiple: true,
                            'collapse-tags': true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: FITMODULE
                    }
                ],
                [
                    {
                        type: 'el-select',
                        prop: 'optionsType',
                        label: '选项形式：',
                        labelWidth: '90px',
                        attrs: {
                            placeholder: '请选择',
                            clearable: false
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: OPTIONTYPE
                    }
                ],
                [
                    {
                        type: 'el-select',
                        prop: 'defaultOption',
                        label: '默认选项：',
                        labelWidth: '90px',
                        attrs: {
                            placeholder: '请先输入下方选项内容',
                            clearable: true
                        },
                        rules: [{ required: true, message: '必填', trigger: 'change' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: this.addFieldForm.optionContents.map((item) => {
                            return {
                                label: item.optionContent,
                                value: item.optionId
                            };
                        })
                    }
                ],
                [
                    {
                        type: 'slot',
                        prop: 'optionContents',
                        label: '选项内容：',
                        labelWidth: '90px',
                        rules: [{ required: false, message: '必填', trigger: 'blur' }],
                        span: 20,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    },
                    {
                        type: 'slot',
                        prop: 'optionList',
                        label: '',
                        labelWidth: '',
                        rules: [],
                        span: 4,
                        isDisabled: false,
                        isShow: true,
                        opts: []
                    }
                ]
            ];
        }
    },
    watch: {
        'addFieldForm.optionContents': {
            handler(newVal) {
                console.log('addFieldForm.optionContents', newVal);
            },
            deep: true,
            immediate: true
        }
    },
    created() {
        this.getTableData(this.pagination);
    },
    methods: {
        handleSearch() {
            this.pagination.curPage = 1;
            this.getTableData(this.pagination);
        },
        getTableData({ curPage = 1, pageSize = this.pagination.pageSize }) {
            this.isLoading = true;
            OptionsContent.page({
                // columnId: 1,
                ...this.searchForm,
                pageNum: curPage,
                pageSize
            })
                .then((res) => {
                    if (res.serviceFlag === 'TRUE') {
                        this.optionsTableData = res.data.list.map((item) => ({
                            rawData: item,
                            id: item.columnId,
                            name: item.columnName,
                            isValid: item.isValid,
                            fitModuleLabel:
                                Array.isArray(item.modulesList) &&
                                item.modulesList
                                    .map((item) => this.getFitModuleLabel(item))
                                    .join(','),
                            optionTypeLabel: this.getOptionTypeLabel(item.optionsType),
                            defaultOption: item.defaultOption
                        }));
                        this.total = res.data.total;
                    } else {
                        this.optionsTableData = [];
                        this.total = 0;
                    }
                })
                .catch((err) => {
                    this.optionsTableData = [];
                    this.total = 0;
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        getFitModuleLabel(value) {
            const item = FITMODULE.find((item) => item.value === value);
            if (item) {
                return item.label;
            }
            return value;
        },
        getOptionTypeLabel(value) {
            const item = OPTIONTYPE.find((item) => item.value === value);
            if (item) {
                return item.label;
            }
            return value;
        },
        // 加载可用选项
        loadAvailableOptions() {
            // 获取所有可用选项
            OptionsContent.getDistinctOptionValues().then((res) => {
                if (res.serviceFlag === 'TRUE') {
                    this.availableOptions = res.data;
                } else {
                    this.availableOptions = [];
                }
            });
        },

        // 判断选项是否已被选择
        isOptionSelected(option) {
            return this.addFieldForm.optionContents.some((item) => item.optionContent === option);
        },

        // 处理选项选择
        handleSelectOption(option) {
            if (!this.isOptionSelected(option)) {
                // 添加选项到 InputToTag 组件
                const id = this.addFieldForm.optionContents.length + 1;
                this.addFieldForm.optionContents.push({ optionContent: option, optionId: id });
            }
        },
        // 打开新增字段弹窗
        handleAddField() {
            this.dialogMode = 'add';
            this.editingFieldId = null;
            this.addFieldForm = this.$options.data().addFieldForm;
            this.fieldDialogVisible = true;
        },

        // 打开编辑字段弹窗
        handleEditField(data) {
            this.dialogMode = 'edit';
            this.editingFieldId = data.columnId;

            // 回显数据
            this.addFieldForm = {
                columnName: data.columnName,
                modules: data.modulesList,
                optionsType: data.optionsType,
                defaultOption: data.optionContents.find((item) => item.isDefaultOptions === 1)
                    .optionId,
                optionContents: data.optionContents.map((item) => {
                    return {
                        optionContent: item.optionContent,
                        optionId: item.optionId
                    };
                })
            };

            this.fieldDialogVisible = true;
        },

        // 保存字段（新增或编辑）
        handleSaveField() {
            this.$refs.addFieldFormRef.validForm().then((valid) => {
                if (valid) {
                    const { columnName, modules, optionsType, defaultOption, optionContents } =
                        this.addFieldForm;

                    let params = {
                        columnName,
                        modules,
                        optionsType,
                        optionContents: optionContents.map(({ optionId, optionContent }) => {
                            return {
                                optionId,
                                optionContent,
                                isDefaultOptions: (optionId === defaultOption && 1) || 0
                            };
                        })
                    };
                    if (this.dialogMode === 'add') {
                        OptionsContent.add(params).then((res) => {
                            if (res.serviceFlag === 'TRUE') {
                                this.$message.success(res.returnMsg || '新增成功');
                                this.fieldDialogVisible = false;
                                this.handleSearch();
                            } else {
                                this.$message.error(res.returnMsg || '新增失败');
                            }
                        });
                    } else {
                        OptionsContent.update({
                            columnId: this.editingFieldId,
                            ...this.addFieldForm
                        }).then((res) => {
                            if (res.serviceFlag === 'TRUE') {
                                this.$message.success(res.returnMsg || '编辑成功');
                                this.fieldDialogVisible = false;
                                this.handleSearch();
                            } else {
                                this.$message.error(res.returnMsg || '编辑失败');
                            }
                        });
                    }
                }
            });
        },
        // 弹窗打开处理
        handleDialogOpen() {
            this.$nextTick(() => {
                this.$refs.addFieldFormRef && this.$refs.addFieldFormRef.clearValidate();
            });
        },
        // 弹窗关闭处理
        handleDialogClose() {
            this.addFieldForm = this.$options.data().addFieldForm;
            this.dialogMode = 'add';
            this.editingFieldId = null;
        },
        handleCardEvent(event) {
            const eventMap = {
                // 编辑卡片
                edit: () => {
                    OptionsContent.getDetails({ columnId: event.params.id }).then((res) => {
                        if (res.serviceFlag === 'TRUE') {
                            this.handleEditField(res.data);
                        } else {
                            this.$message.error(res.returnMsg || '获取字段详情失败');
                        }
                    });
                },

                // 删除卡片
                delete: () => {
                    OptionsContent.remove({
                        columnIds: [event.params]
                    }).then((res) => {
                        this.handleResponse(res, '删除成功', '删除失败');
                    });
                },

                // 切换卡片状态（有效/无效）
                valid: () => {
                    // 实际项目中应该调用真实的API
                    // OptionsContent.changeOptionsContentValid({
                    //     fieldIds: [event.params.id],
                    //     isValid: +event.params.isValid
                    // }).then((res) => {
                    //     this.handleResponse(res, '切换成功', '切换失败');
                    // });

                    // 模拟成功响应
                    // this.$message.success('状态切换成功');
                    this.handleSearch();
                }
            };

            if (eventMap[event.type]) {
                eventMap[event.type]();
            }
        },
        handleResponse(res, successMsg, errorMsg) {
            if (res.serviceFlag === 'TRUE') {
                this.$message.success(res.returnMsg || successMsg);
                this.handleSearch();
            } else {
                this.$message.error(res.returnMsg || errorMsg);
            }
        }
    }
};
</script>

<style scoped lang="less">
.options-content-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    &-header {
        height: 1.25rem;
        margin-left: 0.5rem;
        font-family:
            PingFangSC,
            PingFang SC;
        font-weight: 600;
        font-size: 1rem;
        color: rgba(0, 0, 0, 0.85);
        line-height: 1.25rem;
    }
    &-search {
        height: 3.5rem;
        background: rgba(255, 255, 255, 0.96);
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0 1rem;
    }
    &-main {
        height: 0;
        flex: 1;
        display: flex;
        gap: 1rem;
    }
    .dialog-footer {
        text-align: right;
        margin-top: 32px;
    }
}

// 选项选择器样式
.option-selector {
    max-height: 300px;

    &-content {
        max-height: 240px;
        overflow-y: auto;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        user-select: none;
        &::-webkit-scrollbar {
            width: 4px;
            height: 4px;
        }
        &::-webkit-scrollbar-thumb {
            border-radius: 8px;
            background: #8b8b8b;
        }
        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            border-radius: 8px;
            background: transparent;
        }
        &::-webkit-scrollbar-corner {
            background: rgba(0, 0, 0, 0);
        }
    }
}

.option-tag {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
}

.no-options {
    text-align: center;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    padding: 20px 0;
    width: 100%;
}
</style>
