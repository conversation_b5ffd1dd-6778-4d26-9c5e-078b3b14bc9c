<template>
    <div class="data-integrity-test">
        <h3>数据完整性测试</h3>
        
        <div class="test-section">
            <h4>编辑模式数据完整性测试</h4>
            <p>测试场景：编辑模式下，现有选项的 optionId 字段应该被完整保留</p>
            
            <button @click="initEditData">初始化编辑数据</button>
            <button @click="addNewOption">添加新选项</button>
            <button @click="checkDataIntegrity">检查数据完整性</button>
            
            <div v-if="editData.length > 0">
                <h5>InputToTag 组件:</h5>
                <InputToTag
                    v-model="editData"
                    :props="{ id: 'id', name: 'optionContent' }"
                    placeholder="编辑模式测试 - optionId应该被保留"
                    @change="handleChange"
                    @add="handleAdd"
                    @remove="handleRemove"
                />
                
                <h5>原始输入数据:</h5>
                <pre>{{ JSON.stringify(originalData, null, 2) }}</pre>
                
                <h5>组件输出数据:</h5>
                <pre>{{ JSON.stringify(outputData, null, 2) }}</pre>
                
                <h5>数据完整性检查结果:</h5>
                <div class="integrity-results">
                    <p :class="integrityCheck.optionIdPreserved ? 'success' : 'error'">
                        ✓ optionId 字段保留: {{ integrityCheck.optionIdPreserved ? '通过' : '失败' }}
                    </p>
                    <p :class="integrityCheck.newOptionsNoOptionId ? 'success' : 'error'">
                        ✓ 新选项无 optionId: {{ integrityCheck.newOptionsNoOptionId ? '通过' : '失败' }}
                    </p>
                    <p :class="integrityCheck.allFieldsPreserved ? 'success' : 'error'">
                        ✓ 所有字段保留: {{ integrityCheck.allFieldsPreserved ? '通过' : '失败' }}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h4>新增模式数据测试</h4>
            <p>测试场景：新增模式下，所有选项都不应该有 optionId 字段</p>
            
            <button @click="initAddData">初始化新增数据</button>
            <button @click="addNewOptionInAddMode">添加新选项</button>
            
            <div v-if="addData.length > 0">
                <h5>InputToTag 组件:</h5>
                <InputToTag
                    v-model="addData"
                    :props="{ id: 'id', name: 'optionContent' }"
                    placeholder="新增模式测试 - 不应该有optionId"
                    @change="handleAddModeChange"
                />
                
                <h5>新增模式输出数据:</h5>
                <pre>{{ JSON.stringify(addModeOutputData, null, 2) }}</pre>
                
                <h5>新增模式检查结果:</h5>
                <p :class="!hasAnyOptionId(addModeOutputData) ? 'success' : 'error'">
                    ✓ 无 optionId 字段: {{ !hasAnyOptionId(addModeOutputData) ? '通过' : '失败' }}
                </p>
            </div>
        </div>
    </div>
</template>

<script>
import InputToTag from './InputToTag.vue';

export default {
    name: 'DataIntegrityTest',
    components: {
        InputToTag
    },
    data() {
        return {
            // 编辑模式数据
            editData: [],
            originalData: [],
            outputData: [],
            
            // 新增模式数据
            addData: [],
            addModeOutputData: [],
            
            // 完整性检查结果
            integrityCheck: {
                optionIdPreserved: false,
                newOptionsNoOptionId: false,
                allFieldsPreserved: false
            }
        };
    },
    methods: {
        // 初始化编辑数据（模拟从后端获取的数据）
        initEditData() {
            this.originalData = [
                { 
                    optionContent: '广州大屏', 
                    optionId: 1, 
                    id: 1,
                    isDefaultOptions: 1,
                    createTime: '2024-01-01'
                },
                { 
                    optionContent: '河南大屏', 
                    optionId: 2, 
                    id: 2,
                    isDefaultOptions: 0,
                    createTime: '2024-01-02'
                }
            ];
            this.editData = JSON.parse(JSON.stringify(this.originalData));
        },
        
        // 初始化新增数据
        initAddData() {
            this.addData = [
                { optionContent: '新选项1', id: 1 },
                { optionContent: '新选项2', id: 2 }
            ];
        },
        
        // 添加新选项（编辑模式）
        addNewOption() {
            this.editData.push({
                optionContent: '新增选项' + Date.now().toString().slice(-3),
                id: 0 // 临时ID，组件会重新分配
            });
        },
        
        // 添加新选项（新增模式）
        addNewOptionInAddMode() {
            this.addData.push({
                optionContent: '新选项' + Date.now().toString().slice(-3),
                id: 0
            });
        },
        
        // 处理编辑模式数据变化
        handleChange(outputData, internalData) {
            this.outputData = outputData;
            console.log('编辑模式输出:', outputData);
            console.log('内部数据:', internalData);
        },
        
        // 处理新增模式数据变化
        handleAddModeChange(outputData, internalData) {
            this.addModeOutputData = outputData;
            console.log('新增模式输出:', outputData);
        },
        
        // 处理添加事件
        handleAdd(newTag, allTags) {
            console.log('添加标签:', newTag);
        },
        
        // 处理删除事件
        handleRemove(removedTag, remainingTags) {
            console.log('删除标签:', removedTag);
        },
        
        // 检查数据完整性
        checkDataIntegrity() {
            // 检查 optionId 是否被保留
            const originalWithOptionId = this.originalData.filter(item => item.optionId);
            const outputWithOptionId = this.outputData.filter(item => item.optionId);
            this.integrityCheck.optionIdPreserved = originalWithOptionId.length === outputWithOptionId.length;
            
            // 检查新选项是否没有 optionId
            const newOptions = this.outputData.filter(item => 
                !this.originalData.some(orig => orig.optionContent === item.optionContent)
            );
            this.integrityCheck.newOptionsNoOptionId = newOptions.every(item => !item.optionId);
            
            // 检查所有字段是否被保留
            const originalFields = new Set();
            this.originalData.forEach(item => {
                Object.keys(item).forEach(key => originalFields.add(key));
            });
            
            const outputFields = new Set();
            this.outputData.forEach(item => {
                Object.keys(item).forEach(key => outputFields.add(key));
            });
            
            // 检查重要字段是否保留（除了内部使用的 name 和 id）
            const importantFields = ['optionContent', 'optionId', 'isDefaultOptions', 'createTime'];
            this.integrityCheck.allFieldsPreserved = importantFields.every(field => {
                if (!originalFields.has(field)) return true; // 如果原始数据没有这个字段，就不需要检查
                return outputFields.has(field);
            });
            
            console.log('完整性检查结果:', this.integrityCheck);
        },
        
        // 检查是否有任何 optionId 字段
        hasAnyOptionId(data) {
            return data.some(item => item.hasOwnProperty('optionId'));
        }
    }
};
</script>

<style lang="less" scoped>
.data-integrity-test {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    
    .test-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        
        h4, h5 {
            margin-top: 0;
            color: #303133;
        }
        
        button {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        pre {
            background-color: #f5f7fa;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            overflow-x: auto;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .integrity-results {
            margin-top: 10px;
            
            p {
                margin: 5px 0;
                font-weight: bold;
                
                &.success {
                    color: #67c23a;
                }
                
                &.error {
                    color: #f56c6c;
                }
            }
        }
    }
}
</style>
