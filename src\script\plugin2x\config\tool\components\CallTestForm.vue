<template>
    <el-form ref="callTestForm" :model="formModel" :rules="formRules" class="call-test-form">
        <div class="form-left">
            <div class="request-header-container">
                <header class="form-left-header">
                    <span>请求头</span>
                </header>
                <main class="main-request-header custom-scrollbar">
                    <div
                        v-for="(item, index) in formModel.requestHeader"
                        :key="index"
                        class="request-header-item"
                    >
                        <el-form-item
                            :prop="`requestHeader.${index}.headerName`"
                            :rules="getHeaderRules(item, 'name')"
                        >
                            <template #error="{ error }">
                                <span class="custom-error">
                                    {{ error }}
                                </span>
                            </template>
                            <el-input
                                v-model="formModel.requestHeader[index].headerName"
                                placeholder="参数名"
                            />
                        </el-form-item>
                        <el-form-item
                            :prop="`requestHeader.${index}.headerValue`"
                            :rules="getHeaderRules(item, 'value')"
                        >
                            <template #error="{ error }">
                                <span class="custom-error">
                                    {{ error }}
                                </span>
                            </template>
                            <el-input
                                v-model="formModel.requestHeader[index].headerValue"
                                clearable
                                placeholder="参数值"
                            />
                        </el-form-item>
                    </div>
                </main>
            </div>
            <div class="request-body-container">
                <header class="form-left-header">
                    <span>参数值</span>
                    <i
                        class="el-icon-sort swap-content"
                        @click="swapRequestBody"
                        v-if="requestType === 3"
                    ></i>
                </header>
                <main class="main-request-body">
                    <div class="table-container" v-if="fillByTable">
                        <el-table
                            class="params-table"
                            :data="formModel.tableData"
                            row-key="id"
                            :default-expand-all="true"
                            border
                            height="100%"
                            style="width: 100%"
                            :key="tableDataKey"
                            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                        >
                            <el-table-column prop="label" label="参数" width="200">
                            </el-table-column>
                            <el-table-column prop="value" label="值">
                                <template #default="{ row }">
                                    <el-form-item
                                        :prop="`paramValues.${row.id}`"
                                        :rules="getParamRules(row)"
                                    >
                                        <template #error="{ error }">
                                            <span class="custom-error">
                                                {{ error }}
                                            </span>
                                        </template>
                                        <!-- 非叶节点：仅占位显示 -->
                                        <el-input
                                            v-if="row.children && row.children.length > 0"
                                            class="w-100"
                                            disabled
                                            value=""
                                            :placeholder="getPlaceholder(row)"
                                        />
                                        <!-- 叶节点 Boolean -->
                                        <el-select
                                            v-else-if="row.type === 'boolean'"
                                            class="w-100"
                                            popper-class="mcpservice-theme"
                                            v-model="formModel.paramValues[row.id]"
                                            :placeholder="getPlaceholder(row)"
                                            clearable
                                            @change="handleValueChange(row, $event)"
                                        >
                                            <el-option label="true" value="true" />
                                            <el-option label="false" value="false" />
                                        </el-select>
                                        <!-- 叶节点 其它类型 -->
                                        <el-input
                                            v-else
                                            v-model="formModel.paramValues[row.id]"
                                            :placeholder="getPlaceholder(row)"
                                            clearable
                                            @change="handleValueChange(row, $event)"
                                        />
                                    </el-form-item>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <el-form-item v-else prop="requestBody.stringify" :rules="jsonRules">
                        <template #error="{ error }">
                            <span class="custom-error">
                                {{ error }}
                            </span>
                        </template>
                        <textarea
                            id="request-body"
                            v-model="formModel.requestBody.stringify"
                            :placeholder="requestBodyPlaceholder"
                            class="json-content custom-scrollbar"
                            type="textarea"
                        ></textarea>
                    </el-form-item>
                </main>
            </div>
        </div>
        <el-button type="primary" :disabled="loading" @click="handleStartClick">
            开始测试
        </el-button>
        <div class="form-right">
            <header class="form-right-header">
                <span>测试结果</span>
                <span>
                    {{
                        responseBody.show
                            ? responseBody.isOrigin
                                ? '(原始响应)'
                                : '(过滤后响应)'
                            : ''
                    }}
                </span>
                <i
                    class="el-icon-sort swap-filter"
                    title="原始响应信息和过滤后响应信息切换"
                    @click="swapResponse"
                ></i>
                <i class="el-icon-delete clear-response-body" @click="clearResponseBody"></i>
            </header>
            <main class="form-right-main" v-loading="loading">
                <textarea
                    id="response-body"
                    v-model.trim="responseBody.show"
                    placeholder="接口响应信息处"
                    class="json-content custom-scrollbar"
                    type="textarea"
                    readonly
                ></textarea>
            </main>
        </div>
    </el-form>
</template>

<script>
import { buildTreeFromParams, convertToJson } from '@/script/utils/method';
import ConfigTool from '@/script/api/module/config-tool';

export default {
    name: 'CallTestForm',
    props: {
        apiInfo: {
            type: Object,
            default: () => {}
        },
        paramHeader: {
            type: Array,
            default: () => []
        },
        params: {
            type: Array,
            default: () => []
        },
        paramHandle: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            // 2-Body@form-data, 3-Body@raw
            requestType: 3,
            startCallTimer: null,
            loading: false,
            requestHeader: [
                {
                    headerName: '',
                    headerValue: ''
                }
            ],
            requestBody: {
                stringify: '',
                json: {}
            },
            inParams: [],
            fillByTable: true,
            responseBody: {
                origin: '',
                filter: '',
                isOrigin: true,
                show: ''
            },
            tableData: [],
            tableDataKey: true,
            formModel: {
                requestHeader: [
                    {
                        headerName: '',
                        headerValue: ''
                    }
                ],
                paramValues: {},
                requestBody: {
                    stringify: ''
                }
            },
            jsonRules: [
                {
                    validator: (rule, value, callback) => {
                        if (!value) {
                            // 空串合法，同时清空 json 对象
                            this.requestBody.json = {};
                            callback();
                            return;
                        }
                        try {
                            const parsed = JSON.parse(value);
                            this.requestBody.json = parsed;
                            callback();
                        } catch (e) {
                            callback(new Error('JSON格式错误'));
                        }
                    },
                    trigger: 'blur'
                }
            ],
            requestBodyPlaceholder:
                '// 示例\n' +
                JSON.stringify(
                    {
                        一级参数名1: '一级参数值1',
                        一级参数名2: {
                            二级参数名: '二级参数值'
                        },
                        一级参数名3: ['数组参数值1']
                    },
                    null,
                    8
                )
        };
    },
    watch: {
        paramHeader: {
            handler(newVal) {
                if (newVal.length > 0) {
                    this.requestHeader = newVal.map((item) => ({
                        headerName: item.paramNameEn,
                        headerValue: item.paramDefaultValue || ''
                    }));
                    this.formModel.requestHeader = this.requestHeader;
                } else {
                    this.requestHeader = this.$options.data().requestHeader;
                    this.formModel.requestHeader = this.requestHeader;
                }
                // header 变更后，需重新触发表单校验（必填变动等）
                this.$nextTick(() => {
                    if (this.$refs.callTestForm) {
                        this.$refs.callTestForm.clearValidate();
                        this.$refs.callTestForm.validate(() => {});
                    }
                });
            },
            deep: true,
            immediate: true
        },
        params: {
            handler(newVal) {
                this.formModel.paramValues = {};

                // 过滤掉不起效的节点
                function filterParams(params) {
                    // 1. 找出所有 `isValid === 0` 且 `isLeafNode === 0` 的非叶子节点
                    const invalidNonLeafNodes = params.filter(
                        (param) => param.isValid === 0 && param.isLeafNode === 0
                    );

                    // 2. 收集这些非叶子节点的子节点的 `paramJsonPath` 前缀
                    const childPathsToRemove = new Set();

                    invalidNonLeafNodes.forEach((node) => {
                        if (node.paramType === 'array') {
                            // 数组类型：子节点 path 会是 `parentPath[*].xxx`
                            childPathsToRemove.add(`${node.paramJsonPath}[*]`);
                        } else {
                            // 对象类型：子节点 path 会是 `parentPath.xxx`
                            childPathsToRemove.add(`${node.paramJsonPath}.`);
                        }
                    });

                    // 3. 过滤：
                    //    - 直接过滤掉 `isValid === 0` 的参数
                    //    - 额外过滤掉这些无效非叶子节点的子节点
                    const result = params.filter((param) => {
                        // 情况1：如果 `isValid === 0`，直接过滤
                        if (param.isValid === 0) return false;

                        // 情况2：检查是否是某个无效非叶子节点的子节点
                        for (const pathPrefix of childPathsToRemove) {
                            if (param.paramJsonPath.startsWith(pathPrefix)) {
                                return false;
                            }
                        }

                        return true;
                    });

                    return result;
                }
                const inParamsTEMP = newVal.filter((item) => item.paramInout === 'IN');
                const inParams = filterParams(inParamsTEMP);
                this.inParams = Object.freeze(inParams);

                this.requestType =
                    (inParams[0] && inParams[0].paramInType) || this.$options.data().requestType;

                // 构建树并初始化值（不保留旧输入）
                this.tableData = buildTreeFromParams(inParams);
                this.initParamValues(this.tableData, false);

                // 同步 tableData & formModel
                this.formModel.tableData = this.tableData;

                // 同步 requestBody：优先使用当前表格值转换
                const nowJson = this.produceRequestBodyJson();
                this.requestBody.json = nowJson;
                this.requestBody.stringify = JSON.stringify(nowJson, null, 8);
                this.formModel.requestBody.stringify = this.requestBody.stringify;

                // 触发 table 重渲染
                this.tableDataKey = !this.tableDataKey;

                // 参数变动后重新校验（类型/必填 可能已改变）
                this.$nextTick(() => {
                    if (this.$refs.callTestForm) {
                        this.$refs.callTestForm.clearValidate();
                        this.$refs.callTestForm.validate(() => {});
                    }
                });
            },
            deep: true,
            immediate: true
        },
        // 当用户编辑 textarea 时保持 requestBody 与 formModel 同步
        'formModel.requestBody.stringify'(val) {
            this.requestBody.stringify = val;
            try {
                this.requestBody.json = val ? JSON.parse(val) : {};
            } catch (e) {
                //
            }
        },
        tableData: {
            handler(newVal) {
                this.formModel.tableData = newVal;
            },
            deep: true
        }
    },
    methods: {
        // 点击“开始测试”增加 500ms 防抖，并在请求进行中禁止再次点击
        handleStartClick() {
            if (this.loading) return;
            if (this.startCallTimer) return;
            this.startCallTimer = setTimeout(() => {
                this.startCall();
                clearTimeout(this.startCallTimer);
                this.startCallTimer = null;
            }, 500);
        },
        getHeaderRules(item, type) {
            const headerParam = this.paramHeader.find((h) => h.paramNameEn === item.headerName);
            if (!headerParam || headerParam.isRequired !== 1) {
                return [];
            }
            return [
                {
                    required: true,
                    message: '必填',
                    trigger: ['blur', 'change']
                }
            ];
        },
        /* ---------- 校验辅助 ---------- */
        isEmpty(val) {
            return val === undefined || val === null || val === '';
        },

        getParamRules(row) {
            const param = this.inParams.find((p) => p.paramNameEn === row.label);
            const rules = [];

            const isLeaf = !(row.children && row.children.length > 0);

            // 必填校验（仅叶子节点考虑）
            if (isLeaf && param && param.isRequired === 1) {
                rules.push({
                    required: true,
                    message: '必填',
                    trigger: row.type === 'boolean' ? 'change' : 'blur'
                });
            }

            // 格式校验——叶子节点一定校验；非叶节点仅在用户填写值时校验

            /* eslint-disable complexity */
            rules.push({
                validator: (rule, value, callback) => {
                    // 非叶节点（有子节点）无须校验，直接通过
                    if (!isLeaf) {
                        callback();
                        return;
                    }
                    if (this.isEmpty(value)) {
                        // 非叶节点空值直接通过；叶子节点是否为空已由必填规则决定
                        callback();
                        return;
                    }

                    // 统一放行字符串 literal 'undefined'
                    if (value === 'undefined') {
                        callback();
                        return;
                    }

                    try {
                        switch (row.type) {
                            case 'string':
                                callback(); // 任何值皆可
                                break;
                            case 'int':
                            case 'long':
                                if (!/^-?\d+$/.test(value)) {
                                    callback(new Error('请输入整数'));
                                    return;
                                }
                                // 安全范围
                                const intVal = Number(value);
                                if (
                                    intVal > Number.MAX_SAFE_INTEGER ||
                                    intVal < Number.MIN_SAFE_INTEGER
                                ) {
                                    callback(new Error('数值超出安全范围'));
                                    return;
                                }
                                callback();
                                break;
                            case 'float':
                                if (!/^-?\d*\.?\d+$/.test(value)) {
                                    callback(new Error('请输入数字'));
                                    return;
                                }
                                // 总有效位数<=15
                                const digits = value.replace(/[-.]/g, '').length;
                                if (digits > 15) {
                                    callback(new Error('数字位数不能超过15位'));
                                    return;
                                }
                                callback();
                                break;
                            case 'boolean':
                                if (!['true', 'false', 'null'].includes(value)) {
                                    callback(new Error('请选择 true / false'));
                                    return;
                                }
                                callback();
                                break;
                            case 'array':
                            case 'object':
                                if (value === 'null') {
                                    callback();
                                    return;
                                }
                                const parsed = JSON.parse(value);
                                if (row.type === 'array' && !Array.isArray(parsed)) {
                                    callback(new Error('请输入合法数组'));
                                    return;
                                }
                                if (
                                    row.type === 'object' &&
                                    (Array.isArray(parsed) || typeof parsed !== 'object')
                                ) {
                                    callback(new Error('请输入合法对象'));
                                    return;
                                }
                                callback();
                                break;
                            default:
                                callback();
                        }
                    } catch (err) {
                        callback(
                            new Error(
                                row.type === 'array' || row.type === 'object'
                                    ? 'JSON格式错误'
                                    : '格式错误'
                            )
                        );
                    }
                },
                trigger: row.type === 'boolean' ? 'change' : 'blur'
            });

            return rules;
        },
        getPlaceholder(row) {
            if (row.children && row.children.length > 0) {
                return '已有子节点，无需输入';
            }
            return '请输入';
        },
        handleValueChange(row, value) {
            // 若有子节点，输入框已禁用；防守性同步为 null 但不强制返回错误
            if (row.children && row.children.length > 0) {
                row.value = null;
                this.setDeep(this.formModel.paramValues, row.id, null);
                this.$set(this.formModel.paramValues, row.id, null);
                return;
            }

            // 处理布尔值清空的情况
            if (row.type === 'boolean' && !value) {
                row.value = undefined;
                this.$set(this.formModel.paramValues, row.id, undefined);
                return;
            }

            // 同步更新两处的值
            row.value = value;
            this.setDeep(this.formModel.paramValues, row.id, value);
            this.$set(this.formModel.paramValues, row.id, value);
        },
        // 根据 a.b.c 路径设置嵌套对象值（用于 formModel.paramValues）
        setDeep(obj, path, val) {
            const segs = String(path).split('.');
            let cur = obj;
            segs.forEach((s, idx) => {
                if (idx === segs.length - 1) {
                    this.$set(cur, s, val);
                } else {
                    // 若当前层不是对象，或为空/null，则替换为 {}
                    if (!cur[s] || typeof cur[s] !== 'object') {
                        this.$set(cur, s, {});
                    }
                    cur = cur[s];
                }
            });
        },

        // 深度读取 a.b.c 路径值
        getDeep(obj, path) {
            return String(path)
                .split('.')
                .reduce((acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined), obj);
        },

        produceRequestBodyJson() {
            const defaultJson = convertToJson(this.inParams);
            return defaultJson;
        },

        // 简易 JSONPath 取值（支持通配符 [*] 取第 0 个）
        getValueByPath(obj, jsonPath) {
            const segs = String(jsonPath)
                .replace(/^\$\.?/, '')
                .split('.')
                .filter(Boolean);
            let cur = obj;
            for (let seg of segs) {
                if (seg.endsWith('[*]')) {
                    seg = seg.replace('[*]', '');
                    cur = cur && cur[seg] && cur[seg][0];
                } else if (seg.includes('[')) {
                    const [prop, idxStr] = seg.split(/\[|\]/).filter(Boolean);
                    const idx = Number(idxStr || 0);
                    cur = cur && cur[prop] && cur[prop][idx];
                } else {
                    cur = cur && cur[seg];
                }
                if (cur === undefined) return undefined;
            }
            return cur;
        },

        // 校验转换后的 JSON 是否满足必填与类型
        validateJsonAgainstParams(jsonObj) {
            for (const p of this.inParams) {
                const val = this.getValueByPath(jsonObj, p.paramJsonPath);

                // 必填
                if (p.isRequired === 1) {
                    // null视为已填
                    if (val === undefined || val === '') {
                        this.$message.error(`${p.paramNameEn} 为必填字段`);
                        return false;
                    }
                }

                // 类型检查（若为空且非必填可跳过）
                if (val === undefined || val === null || val === '') {
                    // 空值且非必填，跳过类型校验
                    continue; // eslint-disable-line no-continue
                }

                const type = p.paramType;
                switch (type) {
                    case 'string':
                        if (typeof val !== 'string') {
                            this.$message.error(`${p.paramNameEn} 应为字符串`);
                            return false;
                        }
                        break;
                    case 'int':
                    case 'long':
                        if (!Number.isInteger(val)) {
                            this.$message.error(`${p.paramNameEn} 应为整数`);
                            return false;
                        }
                        break;
                    case 'float':
                        if (typeof val !== 'number') {
                            this.$message.error(`${p.paramNameEn} 应为数字`);
                            return false;
                        }
                        break;
                    case 'boolean':
                        if (typeof val !== 'boolean') {
                            this.$message.error(`${p.paramNameEn} 应为布尔值`);
                            return false;
                        }
                        break;
                    case 'array':
                        if (!Array.isArray(val)) {
                            this.$message.error(`${p.paramNameEn} 应为数组`);
                            return false;
                        }
                        break;
                    case 'object':
                        if (typeof val !== 'object' || Array.isArray(val)) {
                            this.$message.error(`${p.paramNameEn} 应为对象`);
                            return false;
                        }
                        break;
                    default:
                        break;
                }
            }
            return true;
        },
        // 初始化 tableData 同步 formModel.paramValues
        // preserveExisting=true 时尽量保留用户已输入的值
        initParamValues(data, preserveExisting = false) {
            data.forEach((item) => {
                // 计算默认值：object/array 使用 null，其余使用 undefined
                let value;

                if (preserveExisting) {
                    const existed = this.getDeep(this.formModel.paramValues, item.id);
                    if (existed !== undefined) {
                        value = existed;
                    }
                }

                if (value === undefined) {
                    if (item.value !== undefined && item.value !== null && item.value !== '') {
                        value = item.value;
                    } else if (['object', 'array'].includes(item.type)) {
                        value = null;
                    } else {
                        value = undefined;
                    }
                }

                // 嵌套结构 & 扁平 key 双写，防止 v-model 与校验取值不一致
                this.setDeep(this.formModel.paramValues, item.id, value);
                this.$set(this.formModel.paramValues, item.id, value);
                if (item.children && item.children.length > 0) {
                    this.initParamValues(item.children, preserveExisting);
                }

                // 同步节点 value，确保后续 JSON 转换使用最新值
                this.$set(item, 'value', value);
            });
        },
        startCall() {
            const { serviceUri, serviceIp, servicePort, serviceMethod } = this.apiInfo;
            if (!serviceUri || !serviceIp || !servicePort || !serviceMethod) {
                this.$message.warning('请先配置接口服务信息');
                return;
            }

            this.$refs.callTestForm.validate((valid) => {
                if (!valid) {
                    this.$message.warning('请检查是否正确填写');
                    return;
                }

                let header = [];
                if (this.requestHeader.length > 0 && this.requestHeader[0].headerName !== '') {
                    header = this.requestHeader.map((item) => {
                        return {
                            headerName: item.headerName,
                            headerValue: item.headerValue
                        };
                    });
                }
                let paramJson = {};
                if (this.fillByTable) {
                    paramJson = this.transformToNestedJson(this.tableData);
                    if (!this.validateJsonAgainstParams(paramJson)) {
                        return;
                    }
                } else {
                    try {
                        paramJson = JSON.parse(this.formModel.requestBody.stringify || '{}');
                    } catch (e) {
                        this.$message.error('JSON格式错误');
                        return;
                    }

                    if (!this.validateJsonAgainstParams(paramJson)) {
                        return;
                    }
                }

                this.loading = true;
                ConfigTool.callToolTest({
                    requestType: this.requestType,
                    serviceUri,
                    serviceIp,
                    servicePort,
                    serviceMethod,
                    header,
                    paramJson,
                    params: this.params,
                    paramHandle: this.paramHandle
                })
                    .then((res) => {
                        if (res.serviceFlag !== 'TRUE') {
                            this.$message.error(res.returnMsg);
                            return;
                        }
                        this.responseBody.origin = JSON.stringify(res.data.origin, null, 8);
                        this.responseBody.filter = JSON.stringify(res.data.filter, null, 8);
                        this.responseBody.isOrigin = true;
                        this.responseBody.show = this.responseBody.origin;
                    })
                    .catch((err) => {
                        this.$message.error(err.errorMessage);
                    })
                    .finally(() => {
                        this.loading = false;
                    });
            });
        },
        swapRequestBody() {
            this.fillByTable = !this.fillByTable;
        },
        swapResponse() {
            this.responseBody.isOrigin = !this.responseBody.isOrigin;
            if (this.responseBody.isOrigin) {
                this.responseBody.show = this.responseBody.origin;
            } else {
                this.responseBody.show = this.responseBody.filter;
            }
            const resDom = document.getElementById('response-body');
            resDom && (resDom.scrollTop = 0);
        },
        formatJson() {
            try {
                if (this.requestBody.stringify === '') {
                    return true;
                }
                this.requestBody.json = JSON.parse(this.requestBody.stringify);
                return true;
            } catch (e) {
                this.$message.error(`JSON格式错误: ${e.message}`);
                return false;
            }
        },
        /* eslint-disable complexity */
        /* eslint-disable no-ternary */
        transformToNestedJson(dataArray) {
            if (!Array.isArray(dataArray) || dataArray.length === 0) return {};

            const result = {};

            // 将树形结构扁平化处理，便于按照paramJsonPath处理
            const flattenedParams = [];

            const flattenTree = (nodes) => {
                nodes.forEach((node) => {
                    // 提取节点信息
                    const nodeInfo = {
                        paramJsonPath: node.rawJsonPath || '$.' + node.jsonPath, // 优先使用rawJsonPath
                        paramType: node.type,
                        paramDefaultValue: node.value,
                        isLeafNode: node.children && node.children.length > 0 ? 0 : 1
                    };

                    flattenedParams.push(nodeInfo);

                    // 递归处理子节点
                    if (node.children && node.children.length > 0) {
                        flattenTree(node.children);
                    }
                });
            };

            flattenTree(dataArray);

            // 先处理非叶子节点，确保结构完整
            flattenedParams
                .filter((p) => p.isLeafNode === 0)
                .forEach((param) => {
                    const path = param.paramJsonPath;
                    this.setValueByPath(result, path, {});
                });

            // 再处理叶子节点
            flattenedParams
                .filter((p) => p.isLeafNode === 1)
                .forEach((param) => {
                    const path = param.paramJsonPath;
                    const value = this.convertValue(param.paramDefaultValue, param.paramType);
                    this.setValueByPath(result, path, value);
                });

            return result;
        },

        // 递归处理非叶子节点
        processNonLeafNodes(nodes, result) {
            nodes
                .filter((node) => node.children && node.children.length > 0)
                .forEach((node) => {
                    if (node.jsonPath) {
                        this.setValueByPath(result, node.jsonPath, {});
                    }
                    if (node.children && node.children.length > 0) {
                        this.processNonLeafNodes(node.children, result);
                    }
                });
        },

        // 根据JSONPath设置嵌套值
        setValueByPath(obj, path, value) {
            const segments = path.replace(/^\$\.?/, '').split('.');
            let current = obj;

            for (let i = 0; i < segments.length; i++) {
                const segment = segments[i];
                const isLast = i === segments.length - 1;

                // 处理数组通配符 [*]
                if (segment.endsWith('[*]')) {
                    const prop = segment.replace(/\[\*\]$/, '');
                    if (!current[prop]) {
                        current[prop] = [{}];
                    } else if (!Array.isArray(current[prop])) {
                        current[prop] = [current[prop]];
                    } else if (current[prop].length === 0) {
                        current[prop].push({});
                    }

                    if (isLast) {
                        // 如果是最后一个节点，设置值
                        if (Array.isArray(value)) {
                            current[prop] = value;
                        } else if (value !== undefined) {
                            if (current[prop].length === 0) {
                                current[prop].push(value);
                            } else {
                                current[prop][0] = value;
                            }
                        }
                    } else {
                        current = current[prop][0];
                    }
                }
                // 处理数组索引 [n]
                else if (segment.includes('[')) {
                    const [prop, idx] = segment.split(/\[|\]/).filter(Boolean);
                    if (!current[prop]) {
                        current[prop] = [];
                    }
                    let arrayIndex = 0;
                    if (idx) {
                        arrayIndex = parseInt(idx);
                    }

                    // 确保数组足够长
                    while (current[prop].length <= arrayIndex) {
                        if (isLast) {
                            current[prop].push(value);
                        } else {
                            current[prop].push({});
                        }
                    }

                    if (isLast) {
                        current[prop][arrayIndex] = value;
                    } else {
                        current = current[prop][arrayIndex];
                    }
                }
                // 处理普通属性
                else {
                    if (isLast) {
                        current[segment] = value;
                    } else {
                        if (!current[segment]) {
                            current[segment] = {};
                        }
                        current = current[segment];
                    }
                }
            }
        },

        // 类型转换辅助函数
        convertValue(value, type) {
            // 未填写返回处理
            if (value === '') {
                if (type === 'string') return '';
                if (type === 'boolean') return undefined;
                if (type === 'int' || type === 'float' || type === 'double') return undefined;
                if (type === 'array') return undefined;
                if (type === 'object') return undefined;
                return undefined;
            }
            if (value === undefined || value === null) return value;
            // 处理 'undefined'/'null' 字面量
            if (value === 'undefined') {
                return undefined;
            }
            if (value === 'null') {
                return null;
            }

            // 类型转换
            const lowerType = (type || '').toLowerCase();
            switch (lowerType) {
                case 'int':
                case 'integer':
                    return Number.isNaN(Number(value)) ? undefined : parseInt(value, 10);
                case 'long':
                    return Number.isNaN(Number(value)) ? undefined : parseInt(value, 10);
                case 'float':
                case 'double':
                case 'number':
                    return Number.isNaN(Number(value)) ? undefined : Number(value);
                case 'boolean':
                    if (typeof value === 'boolean') return value;
                    if (value === 'true' || value === '1') return true;
                    if (value === 'false' || value === '0') return false;
                    return undefined;
                case 'array':
                    if (Array.isArray(value)) return value;
                    // 尝试解析 JSON 字符串
                    try {
                        const parsed = JSON.parse(value || '[]');
                        return Array.isArray(parsed) ? parsed : undefined;
                    } catch (error) {
                        return undefined;
                    }
                case 'object':
                    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                        return value;
                    }
                    try {
                        const parsed = JSON.parse(value || '{}');
                        return typeof parsed === 'object' &&
                            parsed !== null &&
                            !Array.isArray(parsed)
                            ? parsed
                            : undefined;
                    } catch (error) {
                        return undefined;
                    }
                default:
                    return value;
            }
        },
        clearResponseBody() {
            this.responseBody = '';
        }
    }
};
</script>

<style lang="less" scoped>
.call-test-form {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 1.25rem;
    /deep/ .el-form-item {
        height: 100%;
        margin-bottom: 0;
        background-color: transparent;
        .el-form-item__content {
            height: 100%;
        }
        .custom-error {
            pointer-events: none;
            position: absolute;
            right: 0;
            top: 0;
            padding: 0 6px;
            height: 20px;
            line-height: 20px;
            border-radius: 2px;
            font-size: 10px;
            color: #fff;
            background-color: #f56c6c99;
            z-index: 999;
        }
    }
    .form-left,
    .form-right {
        min-width: 0;
        flex: 1;
        height: 27.625rem;
        background: #ffffff;
        border-radius: 0.25rem;
        border: 0.0625rem solid #d6dae0;
        display: flex;
        flex-direction: column;
    }
    .form-left,
    .form-right {
        &-header {
            height: 2.5rem;
            background: #f6f8fa;
            box-shadow: inset 0 -0.0625rem 0 0 #ebedf0;
            border-radius: 0.25rem 0.25rem 0 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1rem;
            span {
                font-family:
                    PingFangSC,
                    PingFang SC;
                font-weight: 500;
                font-size: 0.875rem;
                color: rgba(0, 0, 0, 0.85);
                line-height: 1.25rem;
            }
            .swap-content {
                font-size: 0.75rem;
                font-weight: 600;
                transform: rotate(90deg);
                cursor: pointer;
                &:hover {
                    color: #1565ff;
                }
            }
            .swap-filter {
                margin-left: auto;
                transform: rotate(90deg);
                font-weight: 500;
                cursor: pointer;
                &:hover {
                    color: #1565ff;
                }
            }
            .clear-response-body {
                margin-left: 0.5rem;
                font-size: 0.75rem;
                font-weight: 500;
                cursor: pointer;
                &:hover {
                    color: #1565ff;
                }
            }
        }
    }
    .request-header-container {
        max-height: 40%;
        display: flex;
        flex-direction: column;
        .main-request-header {
            --bar-width: 0.25rem;
            flex: 1;
            min-height: 0;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            .request-header-item {
                display: grid;
                grid-template-columns: 200px 1fr;
                border-bottom: 0.0625rem solid #d6dae0;
                /deep/ .el-input {
                    &__inner {
                        border-radius: 0;
                        border-top-color: transparent;
                        border-left-color: transparent;
                        border-bottom-color: transparent;
                    }
                }
            }
        }
    }
    .request-body-container {
        min-height: 0;
        flex: 1;
        display: flex;
        flex-direction: column;
        .main-request-body {
            flex: 1;
            min-height: 0;
            .table-container {
                height: 100%;
                /deep/.params-table.el-table {
                    th,
                    td {
                        padding: 0;
                    }
                    .cell {
                        font-family:
                            PingFangSC,
                            PingFang SC;
                        font-weight: 400;
                        font-size: 14px;
                    }
                    .cell:has(.el-input) {
                        padding: 0;
                        .el-input__inner {
                            border-radius: 0;
                            border-color: transparent;
                        }
                    }

                    .el-table__header-wrapper {
                        width: 100%;
                        th {
                            font-family:
                                PingFangSC,
                                PingFang SC;
                            font-weight: 400;
                            font-size: 14px;
                            color: rgba(0, 0, 0, 0.85);
                            line-height: 20px;
                            height: 40px;
                        }
                    }
                    .el-table__body-wrapper {
                        overflow-y: auto;
                        // 自定义滚动条样式
                        &::-webkit-scrollbar {
                            width: 8px;
                        }
                        &::-webkit-scrollbar-thumb {
                            border-radius: 4px;
                            background: #8b8b8b;
                        }
                        &::-webkit-scrollbar-track {
                            /* 滚动条里面轨道 */
                            border-radius: 8px;
                            background: transparent;
                        }
                        &::-webkit-scrollbar-corner {
                            background: rgba(0, 0, 0, 0);
                        }
                    }
                }
            }
        }
    }
    .form-right-main {
        flex: 1;
        min-height: 0;
    }
}
.json-content {
    --bar-width: 0.25rem;
    width: 100%;
    height: 100%;
    outline: none;
    resize: none;
    border: none;
    padding: 0.5rem;
    font-family:
        PingFangSC,
        PingFang SC;
    font-weight: 400;
    font-size: 0.875rem;
    color: rgba(0, 0, 0, 0.85);
    line-height: 1.25rem;
}
/deep/ .el-input__inner:focus {
    border: 0.0625rem solid #1565ff !important;
    box-shadow: 0 0 0 0.125rem rgba(21, 101, 255, 0.2) !important;
}
#request-body {
    border: 0.0625rem solid transparent;
    &:focus:not([readonly]) {
        border: 0.0625rem solid #1565ff !important;
        box-shadow: 0 0 0 0.125rem rgba(21, 101, 255, 0.2) !important;
    }
}
</style>
